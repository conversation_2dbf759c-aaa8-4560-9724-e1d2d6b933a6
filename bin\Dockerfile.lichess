FROM rust:1.87-bookworm AS build

RUN apt-get update && apt-get install -y clang libclang-dev

COPY . /src

RUN cd /src \
   && cargo build --release

FROM python:3.9 AS run

RUN apt-get update && apt-get install -y wget

RUN mkdir /syzygy \
 && wget -e robots=off -r -nH --cut-dirs=2 --no-parent --no-verbose --reject="index.html*" http://tablebase.sesse.net/syzygy/3-4-5/ -P /syzygy

COPY bin/run-bot.sh /run-bot.sh

RUN  git clone https://github.com/ShailChoksi/lichess-bot.git /src \
 && cd /src \
 && git checkout 19207a5fbcfb2462da0eeb1004af897e9270afb7 \
 && pip install -r requirements.txt

COPY bin/config.yml /src/config.yml
COPY bin/human.bin /books/human.bin
COPY --from=build /src/target/release/princhess /engines/princhess

CMD /run-bot.sh

