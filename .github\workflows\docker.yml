name: Docker

on:
  push:
    branches: [ main ]

jobs:
  docker:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Build and push Docker images
      uses: docker/build-push-action@v1
      with:
        dockerfile: bin/Dockerfile.lichess
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
        repository: ianagbip1oti/princhess-lichess
        tags: latest
    - name: Build and push Docker policy images
      uses: docker/build-push-action@v1
      with:
        dockerfile: bin/Dockerfile.lichess.policy
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
        repository: ianagbip1oti/princhess-policy-lichess
        tags: latest
