# Princhess

Princhess is a UCI compatible chess engine written in Rust.

The initial code base was [<PERSON><PERSON><PERSON>](https://github.com/zxqfl/sashimi)

# Links

Look for the princhess channel on the [Engine Programming](https://discord.gg/YctB2p4) discord.

[Emoji Log](https://github.com/ahmadawais/Emoji-Log) is used for commit messages and pull requests.

Can be found on Lichess at https://lichess.org/@/princhess_bot but is very very rarely online

