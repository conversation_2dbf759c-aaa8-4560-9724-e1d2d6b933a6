name: Build

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    strategy:
      matrix:
        include:
          - os: ubuntu-22.04
            cpu: x86-64-v3
          - os: windows-2022
            cpu: x86-64-v3

    runs-on: "${{ matrix.os }}"

    steps:
      - uses: actions/checkout@v2
      - uses: actions/cache@v4
        with:
          path: |
            ${{ env.CARGO_HOME }}/registry
            ${{ env.CARGO_HOME }}/git
            target
          key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}-${{ matrix.cpu }}
          restore-keys: |
            ${{ runner.os }}-cargo-
      - uses: dtolnay/rust-toolchain@stable
        with:
          toolchain: stable
      - run: cargo test
      - run: cargo rustc --release --bin princhess -- -C target-feature=+crt-static -C target-cpu=$TARGET_CPU
        env:
          TARGET_CPU: ${{ matrix.cpu }}
