services:
  elo_check:
    build:
      context: .
      dockerfile: bin/Dockerfile.fastchess
    command:
      -engine cmd=/engines/princhess-0.19.0 name=princhess-0.19.0
      -engine cmd=/engines/princhess-0.18.0 name=princhess-0.18.0

      -each proto=uci tc=8+0.08
            option.SyzygyPath=/syzygy option.Hash=128 option.Threads=1
      -openings file=/books/8moves_v3.epd format=epd order=random
      -games 2 -repeat -rounds 500
      -recover -ratinginterval 10 -concurrency 6
    volumes:
      - ./target/release/princhess:/engines/princhess
      - ./builds/princhess-main:/engines/princhess-main
      - ./builds/princhess-0.19.0:/engines/princhess-0.19.0
      - ./builds/princhess-0.18.0:/engines/princhess-0.18.0
      - ./syzygy:/syzygy:ro

  policy_elo_check:
    build:
      context: .
      dockerfile: bin/Dockerfile.fastchess
    command:
      -engine cmd=/engines/princhess name=princhess
      -engine cmd=/engines/princhess-main name=princhess-main

      -each proto=uci tc=inf nodes=1
            option.SyzygyPath=/syzygy option.Hash=128 option.Threads=1
      -openings file=/books/8moves_v3.epd format=epd order=random
      -games 2 -repeat -rounds 2500
      -recover -ratinginterval 10 -concurrency 6
    volumes:
      - ./target/release/princhess:/engines/princhess
      - ./builds/princhess-main:/engines/princhess-main
      - ./syzygy:/syzygy:ro

  sprt_gain:
    build:
      context: .
      dockerfile: bin/Dockerfile.fastchess
    command:
      -engine cmd=/engines/princhess name=princhess
      -engine cmd=/engines/princhess-main name=princhess-main

      -each proto=uci tc=8+0.08
            option.SyzygyPath=/syzygy option.Hash=128 option.Threads=1
      -sprt elo0=0 elo1=10 alpha=0.05 beta=0.1 model=normalized
      -openings file=/books/UHO_Lichess_4852_v1.epd format=epd order=random
      -games 2 -repeat -rounds 7500
      -recover -ratinginterval 10 -concurrency 6
    volumes:
      - ./target/release/princhess:/engines/princhess
      - ./builds/princhess-main:/engines/princhess-main
      - ./syzygy:/syzygy:ro

  sprt_gain_25k:
    build:
      context: .
      dockerfile: bin/Dockerfile.fastchess
    command:
      -engine cmd=/engines/princhess name=princhess
      -engine cmd=/engines/princhess-main name=princhess-main

      -each proto=uci tc=inf nodes=25000
            option.SyzygyPath=/syzygy option.Hash=128 option.Threads=1
      -sprt elo0=0 elo1=10 alpha=0.05 beta=0.1 model=normalized
      -openings file=/books/UHO_Lichess_4852_v1.epd format=epd order=random
      -games 2 -repeat -rounds 2500
      -recover -ratinginterval 10 -concurrency 6
    volumes:
      - ./target/release/princhess:/engines/princhess
      - ./builds/princhess-main:/engines/princhess-main
      - ./syzygy:/syzygy:ro

  sprt_gain_2t:
    build:
      context: .
      dockerfile: bin/Dockerfile.fastchess
    command:
      -engine cmd=/engines/princhess name=princhess
      -engine cmd=/engines/princhess-main name=princhess-main

      -each proto=uci tc=8+0.08
            option.SyzygyPath=/syzygy option.Hash=256 option.Threads=2
      -sprt elo0=0 elo1=10 alpha=0.05 beta=0.1 model=normalized
      -openings file=/books/UHO_Lichess_4852_v1.epd format=epd order=random
      -games 2 -repeat -rounds 7500
      -recover -ratinginterval 10 -concurrency 3
      -resign movecount=3 score=500 twosided=true
      -draw movenumber=40 movecount=8 score=15
    volumes:
      - ./target/release/princhess:/engines/princhess
      - ./builds/princhess-main:/engines/princhess-main
      - ./syzygy:/syzygy:ro

  sprt_equal_2t:
    build:
      context: .
      dockerfile: bin/Dockerfile.fastchess
    command:
      -engine cmd=/engines/princhess name=princhess
      -engine cmd=/engines/princhess-main name=princhess-main

      -each proto=uci tc=8+0.08
            option.SyzygyPath=/syzygy option.Hash=256 option.Threads=2
      -sprt elo0=-10 elo1=0 alpha=0.05 beta=0.1 model=normalized
      -openings file=/books/UHO_Lichess_4852_v1.epd format=epd order=random
      -games 2 -repeat -rounds 7500
      -recover -ratinginterval 10 -concurrency 3
      -resign movecount=3 score=500 twosided=true
      -draw movenumber=40 movecount=8 score=15
    volumes:
      - ./target/release/princhess:/engines/princhess
      - ./builds/princhess-main:/engines/princhess-main
      - ./syzygy:/syzygy:ro


  sprt_gain_ltc:
    build:
      context: .
      dockerfile: bin/Dockerfile.fastchess
    command:
      -engine cmd=/engines/princhess name=princhess
      -engine cmd=/engines/princhess-main name=princhess-main

      -each proto=uci tc=40+0.4
            option.SyzygyPath=/syzygy option.Hash=128 option.Threads=1
      -sprt elo0=0 elo1=10 alpha=0.1 beta=0.2 model=normalized
      -openings file=/books/UHO_Lichess_4852_v1.epd format=epd order=random
      -games 2 -repeat -rounds 7500
      -recover -ratinginterval 10 -concurrency 6
      -resign movecount=3 score=500 twosided=true
      -draw movenumber=40 movecount=8 score=15
    volumes:
      - ./target/release/princhess:/engines/princhess
      - ./builds/princhess-main:/engines/princhess-main
      - ./syzygy:/syzygy:ro

  sprt_equal:
    build:
      context: .
      dockerfile: bin/Dockerfile.fastchess
    command:
      -engine cmd=/engines/princhess name=princhess
      -engine cmd=/engines/princhess-main name=princhess-main

      -each proto=uci tc=8+0.08
            option.SyzygyPath=/syzygy option.Hash=128 option.Threads=1
      -sprt elo0=-10 elo1=0 alpha=0.05 beta=0.1 model=normalized
      -openings file=/books/UHO_Lichess_4852_v1.epd format=epd order=random
      -games 2 -repeat -rounds 7500
      -recover -ratinginterval 10 -concurrency 6
    volumes:
      - ./target/release/princhess:/engines/princhess
      - ./builds/princhess-main:/engines/princhess-main
      - ./syzygy:/syzygy:ro

  debug:
    build:
      context: .
      dockerfile: bin/Dockerfile.fastchess
    command:
      -engine cmd=/engines/princhess name=princhess
      -engine cmd=/engines/princhess-main name=princhess-main

      -each proto=uci tc=8+0.08
            option.SyzygyPath=/syzygy option.Hash=128 option.Threads=1
      -openings file=/books/UHO_Lichess_4852_v1.epd format=epd order=random
      -rounds 1
      -log file=pgn/debug.log engine=true
      -pgnout /pgn/debug.pgn
    volumes:
      - ./target/release/princhess:/engines/princhess
      - ./builds/princhess-main:/engines/princhess-main
      - ./syzygy:/syzygy:ro
      - ./pgn:/pgn
    environment:
      RUST_BACKTRACE: 1

  debug_6t:
    build:
      context: .
      dockerfile: bin/Dockerfile.fastchess
    command:
      -engine cmd=/engines/princhess name=princhess
      -engine cmd=/engines/princhess-main name=princhess-main

      -each proto=uci tc=8+0.08
            option.SyzygyPath=/syzygy option.Hash=256 option.Threads=6
      -openings file=/books/UHO_Lichess_4852_v1.epd format=epd order=random
      -rounds 50
      -log file=pgn/debug.log engine=true
      -pgnout /pgn/debug.pgn
    volumes:
      - ./target/release/princhess:/engines/princhess
      - ./builds/princhess-main:/engines/princhess-main
      - ./syzygy:/syzygy:ro
      - ./pgn:/pgn
    environment:
      RUST_BACKTRACE: 1

  tune:
    build:
      context: .
      dockerfile: bin/Dockerfile.tune
    volumes:
      - ./target/release/princhess:/engines/princhess
      - ./builds/princhess-main:/engines/princhess-main
      - ./syzygy:/syzygy:ro
      - ./bin/tuning_config.json:/config/tuning_config.json:ro
      - ./train/tuning_data:/data
      - ./train/tuning_plots:/plots

