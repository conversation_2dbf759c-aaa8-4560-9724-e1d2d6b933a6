{"engines": [{"command": "/engines/princhess", "fixed_parameters": {"Threads": 1, "Hash": 128, "SyzygyPath": "/syzygy"}}, {"command": "/engines/princhess-main", "fixed_parameters": {"Threads": 1, "Hash": 128, "SyzygyPath": "/syzygy"}}], "parameter_ranges": {"CPuct": "Integer(1, 100)", "CPuctTau": "Integer(50, 100)", "PolicyTemperatureRoot": "Integer(100, 2000)"}, "rounds": 15, "engine1_npm": "25000", "engine2_npm": "25000", "opening_file": "/books/UH<PERSON>_Lichess_4852_v1.epd", "adjudicate_draws": false, "adjudicate_resign": false, "adjudicate_tb": false, "concurrency": 5, "plot_every": 50, "result_every": 10, "acq_function": "ts"}