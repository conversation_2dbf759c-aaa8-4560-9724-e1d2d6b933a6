FROM python:3.7-bullseye

RUN apt-get update \
 && apt-get install -y gcc g++ gfortran libopenblas-dev liblapack-dev \
 && apt-get install -y curl git build-essential cmake qtbase5-dev qtbase5-dev-tools libqt5svg5-dev unzip \
 && apt-get install -y curl libhdf5-serial-dev libqt5core5a netcdf-bin libnetcdf-dev

RUN git clone https://github.com/cutechess/cutechess.git \
 && cd cutechess \
 && mkdir build \
 && cd build \
 && cmake .. \
 && make

RUN cp cutechess/build/cutechess-cli /usr/local/bin/cutechess-cli

RUN pip3 install cython==0.29.36
RUN pip3 install --no-use-pep517 scikit-learn==0.23.2
RUN pip3 install chess-tuning-tools==0.8.3

RUN mkdir /books
RUN mkdir /engines
RUN mkdir /data

RUN curl -ssL https://github.com/official-stockfish/books/raw/master/UHO_Lichess_4852_v1.epd.zip -o /books/UHO_Lichess_4852_v1.epd.zip
RUN unzip /books/UHO_Lichess_4852_v1.epd.zip -d /books

CMD ["tune", "local", "--verbose", "-c", "/config/tuning_config.json", "-d", "/data/data.npz", "--logfile", "/data/log.txt", "--model-path", "/data/model.pkl"]
