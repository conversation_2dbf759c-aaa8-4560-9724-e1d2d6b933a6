#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Builds Princhess chess engine with native CPU optimizations.

.DESCRIPTION
    This script builds the Princhess UCI chess engine with native CPU optimizations
    for maximum performance on the current machine. It handles compiler detection,
    dependency verification, and provides detailed build output.

.PARAMETER Clean
    Clean the build cache before building (equivalent to 'cargo clean').

.PARAMETER Verbose
    Enable verbose output during the build process.

.PARAMETER TargetCpu
    Specify target CPU architecture. Defaults to 'native' for maximum optimization.
    Other options: x86-64, x86-64-v2, x86-64-v3, x86-64-v4

.PARAMETER CompilerOverride
    Override the C compiler detection. Options: auto, gcc, clang, msvc

.EXAMPLE
    .\build-native.ps1
    Builds with default settings (native CPU optimization)

.EXAMPLE
    .\build-native.ps1 -Clean -Verbose
    Cleans build cache and builds with verbose output

.EXAMPLE
    .\build-native.ps1 -TargetCpu "x86-64-v3" -CompilerOverride "gcc"
    Builds with specific CPU target and forces GCC compiler
#>

[CmdletBinding()]
param(
    [switch]$Clean,
    [switch]$Verbose,
    [string]$TargetCpu = "native",
    [ValidateSet("auto", "gcc", "clang", "msvc")]
    [string]$CompilerOverride = "auto"
)

# Script configuration
$ErrorActionPreference = "Stop"
$ProgressPreference = "SilentlyContinue"

# Colors for output
$Colors = @{
    Success = "Green"
    Warning = "Yellow"
    Error   = "Red"
    Info    = "Cyan"
    Header  = "Magenta"
}

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Colors[$Color]
}

function Write-Header {
    param([string]$Title)
    Write-Host ""
    Write-ColorOutput "=" * 60 -Color "Header"
    Write-ColorOutput " $Title" -Color "Header"
    Write-ColorOutput "=" * 60 -Color "Header"
    Write-Host ""
}

function Test-Command {
    param([string]$Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

function Test-Prerequisites {
    Write-Header "Checking Prerequisites"
    
    $issues = @()
    
    # Check Rust/Cargo
    if (Test-Command "cargo") {
        $cargoVersion = (cargo --version 2>$null)
        Write-ColorOutput "✓ Cargo found: $cargoVersion" -Color "Success"
    }
    else {
        $issues += "Cargo (Rust toolchain) not found. Install from https://rustup.rs/"
    }
    
    # Check C compiler based on override or auto-detection
    $compilerFound = $false
    $compilerUsed = ""
    
    if ($CompilerOverride -eq "auto") {
        # Auto-detect available compilers
        if (Test-Command "clang") {
            $clangVersion = (clang --version 2>$null | Select-Object -First 1)
            Write-ColorOutput "✓ Clang found: $clangVersion" -Color "Success"
            $compilerFound = $true
            $compilerUsed = "clang"
        }
        elseif (Test-Command "gcc") {
            $gccVersion = (gcc --version 2>$null | Select-Object -First 1)
            Write-ColorOutput "✓ GCC found: $gccVersion" -Color "Success"
            $compilerFound = $true
            $compilerUsed = "gcc"
        }
        elseif (Test-Command "cl") {
            Write-ColorOutput "✓ MSVC found" -Color "Success"
            $compilerFound = $true
            $compilerUsed = "msvc"
        }
    }
    else {
        # Check specific compiler
        switch ($CompilerOverride) {
            "clang" {
                if (Test-Command "clang") {
                    $clangVersion = (clang --version 2>$null | Select-Object -First 1)
                    Write-ColorOutput "✓ Clang found: $clangVersion" -Color "Success"
                    $compilerFound = $true
                    $compilerUsed = "clang"
                }
            }
            "gcc" {
                if (Test-Command "gcc") {
                    $gccVersion = (gcc --version 2>$null | Select-Object -First 1)
                    Write-ColorOutput "✓ GCC found: $gccVersion" -Color "Success"
                    $compilerFound = $true
                    $compilerUsed = "gcc"
                }
            }
            "msvc" {
                if (Test-Command "cl") {
                    Write-ColorOutput "✓ MSVC found" -Color "Success"
                    $compilerFound = $true
                    $compilerUsed = "msvc"
                }
            }
        }
    }
    
    if (-not $compilerFound) {
        $issues += "No suitable C compiler found. Install GCC, Clang, or MSVC."
    }
    
    # Check if we're in the right directory
    if (-not (Test-Path "Cargo.toml")) {
        $issues += "Cargo.toml not found. Run this script from the Princhess project root."
    }
    
    if ($issues.Count -gt 0) {
        Write-ColorOutput "❌ Prerequisites check failed:" -Color "Error"
        foreach ($issue in $issues) {
            Write-ColorOutput "   • $issue" -Color "Error"
        }
        exit 1
    }
    
    Write-ColorOutput "✓ All prerequisites satisfied" -Color "Success"
    return $compilerUsed
}

function Invoke-BuildStep {
    param(
        [string]$StepName,
        [scriptblock]$Action
    )
    
    Write-ColorOutput "🔨 $StepName..." -Color "Info"
    $stopwatch = [System.Diagnostics.Stopwatch]::StartNew()
    
    try {
        & $Action
        $stopwatch.Stop()
        Write-ColorOutput "✓ $StepName completed in $($stopwatch.Elapsed.TotalSeconds.ToString('F1'))s" -Color "Success"
    }
    catch {
        $stopwatch.Stop()
        Write-ColorOutput "❌ $StepName failed: $($_.Exception.Message)" -Color "Error"
        throw
    }
}

function Update-BuildScript {
    param([string]$Compiler)
    
    # Only modify build.rs if we need to use GCC instead of clang on Windows
    if ($IsWindows -and $Compiler -eq "gcc") {
        Write-ColorOutput "📝 Temporarily modifying build.rs to use GCC..." -Color "Info"
        
        $buildRsPath = "build.rs"
        $buildRsContent = Get-Content $buildRsPath -Raw
        $originalContent = $buildRsContent
        
        # Replace clang with gcc in the Windows-specific section
        $modifiedContent = $buildRsContent -replace 'cc\.compiler\("clang"\);', 'cc.compiler("gcc");'
        
        if ($modifiedContent -ne $originalContent) {
            Set-Content $buildRsPath $modifiedContent -NoNewline
            Write-ColorOutput "✓ Build script updated to use GCC" -Color "Success"
            return $true
        }
    }
    return $false
}

function Restore-BuildScript {
    param([bool]$WasModified)
    
    if ($WasModified) {
        Write-ColorOutput "📝 Restoring original build.rs..." -Color "Info"
        
        $buildRsPath = "build.rs"
        $buildRsContent = Get-Content $buildRsPath -Raw
        
        # Restore clang
        $restoredContent = $buildRsContent -replace 'cc\.compiler\("gcc"\);', 'cc.compiler("clang");'
        Set-Content $buildRsPath $restoredContent -NoNewline
        
        Write-ColorOutput "✓ Build script restored" -Color "Success"
    }
}

# Main execution
try {
    Write-Header "Princhess Native Build Script"
    Write-ColorOutput "Target CPU: $TargetCpu" -Color "Info"
    Write-ColorOutput "Compiler Override: $CompilerOverride" -Color "Info"
    Write-Host ""
    
    # Check prerequisites and get compiler info
    $detectedCompiler = Test-Prerequisites
    
    # Clean if requested
    if ($Clean) {
        Invoke-BuildStep "Cleaning build cache" {
            cargo clean
        }
    }
    
    # Update build script if needed
    $buildScriptModified = Update-BuildScript -Compiler $detectedCompiler
    
    try {
        # Set environment variables
        $env:TARGET_CPU = $TargetCpu
        if ($detectedCompiler -eq "gcc") {
            $env:CC = "gcc"
            $env:CXX = "g++"
        }
        
        # Build command arguments
        $buildArgs = @(
            "rustc"
            "--release"
            "--bin", "princhess"
            "--"
            "-C", "target-cpu=$TargetCpu"
        )
        
        if ($Verbose) {
            $buildArgs = @("--verbose") + $buildArgs
        }
        
        # Execute build
        Invoke-BuildStep "Building Princhess with native optimizations" {
            & cargo @buildArgs
        }
        
        # Verify build output
        $exePath = if ($IsWindows) { "target\release\princhess.exe" } else { "target/release/princhess" }
        if (Test-Path $exePath) {
            $fileInfo = Get-Item $exePath
            Write-Header "Build Successful!"
            Write-ColorOutput "✓ Executable created: $exePath" -Color "Success"
            Write-ColorOutput "✓ File size: $([math]::Round($fileInfo.Length / 1MB, 2)) MB" -Color "Success"
            Write-ColorOutput "✓ Last modified: $($fileInfo.LastWriteTime)" -Color "Success"
            
            # Test the executable
            Write-ColorOutput "🧪 Testing executable..." -Color "Info"
            $testResult = echo "uci" | & $exePath | Select-Object -First 1
            if ($testResult -match "id name Princhess") {
                Write-ColorOutput "✓ Executable test passed" -Color "Success"
            }
            else {
                Write-ColorOutput "⚠ Executable test inconclusive" -Color "Warning"
            }
        }
        else {
            throw "Build completed but executable not found at $exePath"
        }
    }
    finally {
        # Always restore build script
        Restore-BuildScript -WasModified $buildScriptModified
    }
    
    Write-Host ""
    Write-ColorOutput "🎉 Native build completed successfully!" -Color "Success"
    Write-ColorOutput "   You can now use the optimized Princhess engine at: $exePath" -Color "Info"
}
catch {
    Write-Host ""
    Write-ColorOutput "💥 Build failed: $($_.Exception.Message)" -Color "Error"
    exit 1
}
