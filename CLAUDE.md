# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Princhess is a UCI-compatible chess engine written in Rust that uses Monte Carlo Tree Search (MCTS) with neural network evaluation. It was initially based on the Sashimi chess engine.

## Common Development Commands

### Building
```bash
# Standard release build
cargo build --release

# Native CPU optimization build (recommended for best performance)
make native
```

### Code Quality
```bash
# Format code (MUST run after any code changes)
cargo fmt

# Check for common mistakes and code quality issues
cargo clippy
```

### Running the Engine
```bash
# Interactive UCI mode
./target/release/princhess

# Execute UCI commands directly
./target/release/princhess "uci" "position startpos" "go movetime 1000"

# Run built-in benchmark
./target/release/princhess bench
```

### Testing
```bash
# ELO regression testing (requires Docker)
docker-compose up elo_check

# Test for improvements using SPRT
docker-compose up sprt_gain

# Debug games with logging
docker-compose up debug
```

## High-Level Architecture

### Core Engine Structure
- **UCI Protocol**: Entry point is `src/uci.rs` which handles all GUI communication
- **Search Algorithm**: MCTS implementation in `src/search.rs` with tree structure in `src/search_tree.rs`
- **Neural Networks**: Pre-trained networks in `src/nets/` (policy.bin, value.bin) evaluated via `src/policy.rs` and `src/value.rs`
- **Board Representation**: Bitboard-based implementation in `src/chess/` module
- **Memory Management**: Custom arena allocator in `src/arena.rs` for efficient tree node allocation
- **Transposition Table**: Position caching in `src/transposition_table.rs`
- **Tablebase Support**: Syzygy tablebase integration via Fathom library in `src/tablebase/`

### Key Design Patterns
- **Type Safety**: Extensive use of Rust's type system for move validation and board state
- **Zero-Copy**: Efficient memory usage through arena allocation for search trees
- **Feature Flags**: Neural networks can be disabled via Cargo features
- **External C Library**: Fathom tablebase library integrated via bindgen

### Neural Network Integration
- Uses Goober library for neural network operations
- Policy network guides move selection during MCTS
- Value network evaluates terminal positions
- Networks are embedded as binary resources at compile time

## Commit Message Convention

Use Emoji Log format with these prefixes:
- 📦 NEW: Add something entirely new
- 👌 IMP: Improve/enhance/refactor existing code
- 🐛 FIX: Fix a bug
- 📖 DOC: Add or update documentation
- 🤖 TST: Add or update tests

Messages should be imperative, action-based, and concise (under 50 characters).

## Development Principles

- Follow KISS, YAGNI, and DRY principles
- Prefer self-documenting code over comments
- When comments are necessary, explain why, not what
- Always run `cargo fmt` after code changes
- Use `cargo clippy` to maintain code quality