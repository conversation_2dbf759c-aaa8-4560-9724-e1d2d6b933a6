/* automatically generated by rust-bindgen 0.69.1 */

pub const TB_VALUE_PAWN: u32 = 100;
pub const TB_VALUE_MATE: u32 = 32000;
pub const TB_VALUE_INFINITE: u32 = 32767;
pub const TB_VALUE_DRAW: u32 = 0;
pub const TB_MAX_MATE_PLY: u32 = 255;
pub const _STDINT_H: u32 = 1;
pub const _FEATURES_H: u32 = 1;
pub const _DEFAULT_SOURCE: u32 = 1;
pub const __GLIBC_USE_ISOC2X: u32 = 0;
pub const __USE_ISOC11: u32 = 1;
pub const __USE_ISOC99: u32 = 1;
pub const __USE_ISOC95: u32 = 1;
pub const __USE_POSIX_IMPLICITLY: u32 = 1;
pub const _POSIX_SOURCE: u32 = 1;
pub const _POSIX_C_SOURCE: u32 = 200809;
pub const __USE_POSIX: u32 = 1;
pub const __USE_POSIX2: u32 = 1;
pub const __USE_POSIX199309: u32 = 1;
pub const __USE_POSIX199506: u32 = 1;
pub const __USE_XOPEN2K: u32 = 1;
pub const __USE_XOPEN2K8: u32 = 1;
pub const _ATFILE_SOURCE: u32 = 1;
pub const __USE_MISC: u32 = 1;
pub const __USE_ATFILE: u32 = 1;
pub const __USE_FORTIFY_LEVEL: u32 = 0;
pub const __GLIBC_USE_DEPRECATED_GETS: u32 = 0;
pub const __GLIBC_USE_DEPRECATED_SCANF: u32 = 0;
pub const _STDC_PREDEF_H: u32 = 1;
pub const __STDC_IEC_559__: u32 = 1;
pub const __STDC_IEC_559_COMPLEX__: u32 = 1;
pub const __STDC_ISO_10646__: u32 = 201706;
pub const __GNU_LIBRARY__: u32 = 6;
pub const __GLIBC__: u32 = 2;
pub const __GLIBC_MINOR__: u32 = 31;
pub const _SYS_CDEFS_H: u32 = 1;
pub const __glibc_c99_flexarr_available: u32 = 1;
pub const __WORDSIZE: u32 = 64;
pub const __WORDSIZE_TIME64_COMPAT32: u32 = 0;
pub const __LONG_DOUBLE_USES_FLOAT128: u32 = 0;
pub const __HAVE_GENERIC_SELECTION: u32 = 1;
pub const __GLIBC_USE_LIB_EXT2: u32 = 0;
pub const __GLIBC_USE_IEC_60559_BFP_EXT: u32 = 0;
pub const __GLIBC_USE_IEC_60559_BFP_EXT_C2X: u32 = 0;
pub const __GLIBC_USE_IEC_60559_FUNCS_EXT: u32 = 0;
pub const __GLIBC_USE_IEC_60559_FUNCS_EXT_C2X: u32 = 0;
pub const __GLIBC_USE_IEC_60559_TYPES_EXT: u32 = 0;
pub const _BITS_TYPES_H: u32 = 1;
pub const __TIMESIZE: u32 = 64;
pub const _BITS_TYPESIZES_H: u32 = 1;
pub const __OFF_T_MATCHES_OFF64_T: u32 = 1;
pub const __INO_T_MATCHES_INO64_T: u32 = 1;
pub const __RLIM_T_MATCHES_RLIM64_T: u32 = 1;
pub const __STATFS_MATCHES_STATFS64: u32 = 1;
pub const __FD_SETSIZE: u32 = 1024;
pub const _BITS_TIME64_H: u32 = 1;
pub const _BITS_WCHAR_H: u32 = 1;
pub const _BITS_STDINT_INTN_H: u32 = 1;
pub const _BITS_STDINT_UINTN_H: u32 = 1;
pub const INT8_MIN: i32 = -128;
pub const INT16_MIN: i32 = -32768;
pub const INT32_MIN: i32 = -2147483648;
pub const INT8_MAX: u32 = 127;
pub const INT16_MAX: u32 = 32767;
pub const INT32_MAX: u32 = 2147483647;
pub const UINT8_MAX: u32 = 255;
pub const UINT16_MAX: u32 = 65535;
pub const UINT32_MAX: u32 = 4294967295;
pub const INT_LEAST8_MIN: i32 = -128;
pub const INT_LEAST16_MIN: i32 = -32768;
pub const INT_LEAST32_MIN: i32 = -2147483648;
pub const INT_LEAST8_MAX: u32 = 127;
pub const INT_LEAST16_MAX: u32 = 32767;
pub const INT_LEAST32_MAX: u32 = 2147483647;
pub const UINT_LEAST8_MAX: u32 = 255;
pub const UINT_LEAST16_MAX: u32 = 65535;
pub const UINT_LEAST32_MAX: u32 = 4294967295;
pub const INT_FAST8_MIN: i32 = -128;
pub const INT_FAST16_MIN: i64 = -9223372036854775808;
pub const INT_FAST32_MIN: i64 = -9223372036854775808;
pub const INT_FAST8_MAX: u32 = 127;
pub const INT_FAST16_MAX: u64 = 9223372036854775807;
pub const INT_FAST32_MAX: u64 = 9223372036854775807;
pub const UINT_FAST8_MAX: u32 = 255;
pub const UINT_FAST16_MAX: i32 = -1;
pub const UINT_FAST32_MAX: i32 = -1;
pub const INTPTR_MIN: i64 = -9223372036854775808;
pub const INTPTR_MAX: u64 = 9223372036854775807;
pub const UINTPTR_MAX: i32 = -1;
pub const PTRDIFF_MIN: i64 = -9223372036854775808;
pub const PTRDIFF_MAX: u64 = 9223372036854775807;
pub const SIG_ATOMIC_MIN: i32 = -2147483648;
pub const SIG_ATOMIC_MAX: u32 = 2147483647;
pub const SIZE_MAX: i32 = -1;
pub const WINT_MIN: u32 = 0;
pub const WINT_MAX: u32 = 4294967295;
pub const true_: u32 = 1;
pub const false_: u32 = 0;
pub const __bool_true_false_are_defined: u32 = 1;
pub const TB_MAX_MOVES: u32 = 193;
pub const TB_MAX_CAPTURES: u32 = 64;
pub const TB_MAX_PLY: u32 = 256;
pub const TB_CASTLING_K: u32 = 1;
pub const TB_CASTLING_Q: u32 = 2;
pub const TB_CASTLING_k: u32 = 4;
pub const TB_CASTLING_q: u32 = 8;
pub const TB_LOSS: u32 = 0;
pub const TB_BLESSED_LOSS: u32 = 1;
pub const TB_DRAW: u32 = 2;
pub const TB_CURSED_WIN: u32 = 3;
pub const TB_WIN: u32 = 4;
pub const TB_PROMOTES_NONE: u32 = 0;
pub const TB_PROMOTES_QUEEN: u32 = 1;
pub const TB_PROMOTES_ROOK: u32 = 2;
pub const TB_PROMOTES_BISHOP: u32 = 3;
pub const TB_PROMOTES_KNIGHT: u32 = 4;
pub const TB_RESULT_WDL_MASK: u32 = 15;
pub const TB_RESULT_TO_MASK: u32 = 1008;
pub const TB_RESULT_FROM_MASK: u32 = 64512;
pub const TB_RESULT_PROMOTES_MASK: u32 = 458752;
pub const TB_RESULT_EP_MASK: u32 = 524288;
pub const TB_RESULT_DTZ_MASK: u32 = 4293918720;
pub const TB_RESULT_WDL_SHIFT: u32 = 0;
pub const TB_RESULT_TO_SHIFT: u32 = 4;
pub const TB_RESULT_FROM_SHIFT: u32 = 10;
pub const TB_RESULT_PROMOTES_SHIFT: u32 = 16;
pub const TB_RESULT_EP_SHIFT: u32 = 19;
pub const TB_RESULT_DTZ_SHIFT: u32 = 20;
pub const TB_RESULT_FAILED: u32 = 4294967295;
pub type __u_char = ::std::os::raw::c_uchar;
pub type __u_short = ::std::os::raw::c_ushort;
pub type __u_int = ::std::os::raw::c_uint;
pub type __u_long = ::std::os::raw::c_ulong;
pub type __int8_t = ::std::os::raw::c_schar;
pub type __uint8_t = ::std::os::raw::c_uchar;
pub type __int16_t = ::std::os::raw::c_short;
pub type __uint16_t = ::std::os::raw::c_ushort;
pub type __int32_t = ::std::os::raw::c_int;
pub type __uint32_t = ::std::os::raw::c_uint;
pub type __int64_t = ::std::os::raw::c_long;
pub type __uint64_t = ::std::os::raw::c_ulong;
pub type __int_least8_t = __int8_t;
pub type __uint_least8_t = __uint8_t;
pub type __int_least16_t = __int16_t;
pub type __uint_least16_t = __uint16_t;
pub type __int_least32_t = __int32_t;
pub type __uint_least32_t = __uint32_t;
pub type __int_least64_t = __int64_t;
pub type __uint_least64_t = __uint64_t;
pub type __quad_t = ::std::os::raw::c_long;
pub type __u_quad_t = ::std::os::raw::c_ulong;
pub type __intmax_t = ::std::os::raw::c_long;
pub type __uintmax_t = ::std::os::raw::c_ulong;
pub type __dev_t = ::std::os::raw::c_ulong;
pub type __uid_t = ::std::os::raw::c_uint;
pub type __gid_t = ::std::os::raw::c_uint;
pub type __ino_t = ::std::os::raw::c_ulong;
pub type __ino64_t = ::std::os::raw::c_ulong;
pub type __mode_t = ::std::os::raw::c_uint;
pub type __nlink_t = ::std::os::raw::c_uint;
pub type __off_t = ::std::os::raw::c_long;
pub type __off64_t = ::std::os::raw::c_long;
pub type __pid_t = ::std::os::raw::c_int;
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct __fsid_t {
    pub __val: [::std::os::raw::c_int; 2usize],
}
pub type __clock_t = ::std::os::raw::c_long;
pub type __rlim_t = ::std::os::raw::c_ulong;
pub type __rlim64_t = ::std::os::raw::c_ulong;
pub type __id_t = ::std::os::raw::c_uint;
pub type __time_t = ::std::os::raw::c_long;
pub type __useconds_t = ::std::os::raw::c_uint;
pub type __suseconds_t = ::std::os::raw::c_long;
pub type __daddr_t = ::std::os::raw::c_int;
pub type __key_t = ::std::os::raw::c_int;
pub type __clockid_t = ::std::os::raw::c_int;
pub type __timer_t = *mut ::std::os::raw::c_void;
pub type __blksize_t = ::std::os::raw::c_int;
pub type __blkcnt_t = ::std::os::raw::c_long;
pub type __blkcnt64_t = ::std::os::raw::c_long;
pub type __fsblkcnt_t = ::std::os::raw::c_ulong;
pub type __fsblkcnt64_t = ::std::os::raw::c_ulong;
pub type __fsfilcnt_t = ::std::os::raw::c_ulong;
pub type __fsfilcnt64_t = ::std::os::raw::c_ulong;
pub type __fsword_t = ::std::os::raw::c_long;
pub type __ssize_t = ::std::os::raw::c_long;
pub type __syscall_slong_t = ::std::os::raw::c_long;
pub type __syscall_ulong_t = ::std::os::raw::c_ulong;
pub type __loff_t = __off64_t;
pub type __caddr_t = *mut ::std::os::raw::c_char;
pub type __intptr_t = ::std::os::raw::c_long;
pub type __socklen_t = ::std::os::raw::c_uint;
pub type __sig_atomic_t = ::std::os::raw::c_int;
pub type int_least8_t = __int_least8_t;
pub type int_least16_t = __int_least16_t;
pub type int_least32_t = __int_least32_t;
pub type int_least64_t = __int_least64_t;
pub type uint_least8_t = __uint_least8_t;
pub type uint_least16_t = __uint_least16_t;
pub type uint_least32_t = __uint_least32_t;
pub type uint_least64_t = __uint_least64_t;
pub type int_fast8_t = ::std::os::raw::c_schar;
pub type int_fast16_t = ::std::os::raw::c_long;
pub type int_fast32_t = ::std::os::raw::c_long;
pub type int_fast64_t = ::std::os::raw::c_long;
pub type uint_fast8_t = ::std::os::raw::c_uchar;
pub type uint_fast16_t = ::std::os::raw::c_ulong;
pub type uint_fast32_t = ::std::os::raw::c_ulong;
pub type uint_fast64_t = ::std::os::raw::c_ulong;
pub type intmax_t = __intmax_t;
pub type uintmax_t = __uintmax_t;
extern "C" {
    pub fn tb_init_impl(_path: *const ::std::os::raw::c_char) -> bool;
}
extern "C" {
    pub fn tb_probe_wdl_impl(
        _white: u64,
        _black: u64,
        _kings: u64,
        _queens: u64,
        _rooks: u64,
        _bishops: u64,
        _knights: u64,
        _pawns: u64,
        _ep: ::std::os::raw::c_uint,
        _turn: bool,
    ) -> ::std::os::raw::c_uint;
}
extern "C" {
    pub fn tb_probe_root_impl(
        _white: u64,
        _black: u64,
        _kings: u64,
        _queens: u64,
        _rooks: u64,
        _bishops: u64,
        _knights: u64,
        _pawns: u64,
        _rule50: ::std::os::raw::c_uint,
        _ep: ::std::os::raw::c_uint,
        _turn: bool,
        _results: *mut ::std::os::raw::c_uint,
    ) -> ::std::os::raw::c_uint;
}
extern "C" {
    pub static mut TB_LARGEST: ::std::os::raw::c_uint;
}
extern "C" {
    pub fn tb_init(_path: *const ::std::os::raw::c_char) -> bool;
}
extern "C" {
    pub fn tb_free();
}
extern "C" {
    pub fn tb_probe_wdl(
        _white: u64,
        _black: u64,
        _kings: u64,
        _queens: u64,
        _rooks: u64,
        _bishops: u64,
        _knights: u64,
        _pawns: u64,
        _rule50: ::std::os::raw::c_uint,
        _castling: ::std::os::raw::c_uint,
        _ep: ::std::os::raw::c_uint,
        _turn: bool,
    ) -> ::std::os::raw::c_uint;
}
extern "C" {
    pub fn tb_probe_root(
        _white: u64,
        _black: u64,
        _kings: u64,
        _queens: u64,
        _rooks: u64,
        _bishops: u64,
        _knights: u64,
        _pawns: u64,
        _rule50: ::std::os::raw::c_uint,
        _castling: ::std::os::raw::c_uint,
        _ep: ::std::os::raw::c_uint,
        _turn: bool,
        _results: *mut ::std::os::raw::c_uint,
    ) -> ::std::os::raw::c_uint;
}
pub type TbMove = u16;
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct TbRootMove {
    pub move_: TbMove,
    pub pv: [TbMove; 256usize],
    pub pvSize: ::std::os::raw::c_uint,
    pub tbScore: i32,
    pub tbRank: i32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct TbRootMoves {
    pub size: ::std::os::raw::c_uint,
    pub moves: [TbRootMove; 193usize],
}
extern "C" {
    pub fn tb_probe_root_dtz(
        _white: u64,
        _black: u64,
        _kings: u64,
        _queens: u64,
        _rooks: u64,
        _bishops: u64,
        _knights: u64,
        _pawns: u64,
        _rule50: ::std::os::raw::c_uint,
        _castling: ::std::os::raw::c_uint,
        _ep: ::std::os::raw::c_uint,
        _turn: bool,
        hasRepeated: bool,
        useRule50: bool,
        _results: *mut TbRootMoves,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn tb_probe_root_wdl(
        _white: u64,
        _black: u64,
        _kings: u64,
        _queens: u64,
        _rooks: u64,
        _bishops: u64,
        _knights: u64,
        _pawns: u64,
        _rule50: ::std::os::raw::c_uint,
        _castling: ::std::os::raw::c_uint,
        _ep: ::std::os::raw::c_uint,
        _turn: bool,
        useRule50: bool,
        _results: *mut TbRootMoves,
    ) -> ::std::os::raw::c_int;
}
extern "C" {
    pub fn tb_pop_count(_bb: u64) -> ::std::os::raw::c_uint;
}
extern "C" {
    pub fn tb_lsb(_bb: u64) -> ::std::os::raw::c_uint;
}
extern "C" {
    pub fn tb_pop_lsb(_bb: u64) -> u64;
}
extern "C" {
    pub fn tb_king_attacks(_square: ::std::os::raw::c_uint) -> u64;
}
extern "C" {
    pub fn tb_queen_attacks(_square: ::std::os::raw::c_uint, _occ: u64) -> u64;
}
extern "C" {
    pub fn tb_rook_attacks(_square: ::std::os::raw::c_uint, _occ: u64) -> u64;
}
extern "C" {
    pub fn tb_bishop_attacks(_square: ::std::os::raw::c_uint, _occ: u64) -> u64;
}
extern "C" {
    pub fn tb_knight_attacks(_square: ::std::os::raw::c_uint) -> u64;
}
extern "C" {
    pub fn tb_pawn_attacks(_square: ::std::os::raw::c_uint, _color: bool) -> u64;
}
