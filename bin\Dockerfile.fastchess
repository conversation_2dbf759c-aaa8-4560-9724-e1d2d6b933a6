FROM debian:bullseye

RUN apt-get update \
 && apt-get install -y curl git build-essential unzip

RUN git clone https://github.com/Disservin/fastchess.git \
 && cd fastchess \
 && make

RUN mkdir /books
RUN mkdir /engines

RUN curl -ssL https://github.com/official-stockfish/books/raw/master/UHO_Lichess_4852_v1.epd.zip -o /books/UHO_Lichess_4852_v1.epd.zip
RUN curl -ssL https://github.com/AndyGrant/openbench-books/raw/master/8moves_v3.epd.zip -o /books/8moves_v3.epd.zip
RUN unzip /books/UHO_Lichess_4852_v1.epd.zip -d /books
RUN unzip /books/8moves_v3.epd.zip -d /books

ENTRYPOINT ["fastchess/fastchess"]
