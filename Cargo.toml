[package]
authors = ["<PERSON> <<EMAIL>>"]
name = "princhess"
description = "A UCI compatible chess engine using MCTS search aimed at running on the CPU"
version = "0.0.0-dev"
edition = "2021"
license = "GPL-3.0"
repository = "https://github.com/princesslana/princhess"
keywords = ["chess", "uci", "mcts"]
categories = ["games"]

[[bin]]
name = "princhess"
path = "src/main.rs"

[dependencies]
arrayvec = "=0.7.6"
bytemuck = { version = "=1.21.0", features = ["derive", "extern_crate_alloc", "min_const_generics", "must_cast"] }
bytemuck_derive = "=1.8.1"
fastapprox = "=0.3.1"
goober = { git = "https://github.com/jw1912/goober/", rev = "32b9b52" }
nohash-hasher = "=0.2.0"
memmap = "=0.7.0"
scc = "=2.3.0"
scoped_threadpool = "=0.1.9"

[features]
default = ["policy-net", "value-net"]
policy-net = []
value-net = []

[build-dependencies]
bindgen = "=0.69.1"
cc = "=1.0.83"

[profile.release]
lto = true
debug = true
