name: Release

on:
  push:
    tags: ["*.*.*"]

jobs:
  release:
    runs-on: ubuntu-22.04
    outputs:
      upload_url: ${{ steps.create_release.outputs.upload_url }}
    steps:
      - name: Create Release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref }}
          release_name: Release ${{ github.ref }}
          draft: false
          prerelease: false
  build:
    strategy:
      matrix:
        # Skip macos for now, since ends up with the same filename as ubuntu
        # which means the upload glob needs some more work
        os: ["ubuntu-22.04", "windows-2022"]
        cpu: ["x86-64-v3"]

    runs-on: "${{ matrix.os }}"
    needs: release

    steps:
      - uses: actions/checkout@v2
      - uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
      - name: Set version
        run: sed -i "s/^version = .*/version = \"$(git describe --tags --dirty --always)\"/" Cargo.toml
        shell: bash
      - run: cargo rustc --release --bin princhess -- -C target-feature=+crt-static -C target-cpu=$TARGET_CPU
        env:
          TARGET_CPU: ${{ matrix.cpu }}
      - run: ls target/release
      - name: Upload Assets to Release
        uses: csexton/release-asset-action@v2
        with:
          pattern: "target/release/princhess@(|.exe)"
          github-token: ${{ secrets.GITHUB_TOKEN }}
          release-url: ${{ needs.release.outputs.upload_url }}
