use crate::math::Rng;
use crate::search::SCALE;
use crate::search_tree::MoveEdge;
use std::f32;

/// Gumbel distribution sampling and utilities for Gumbel AlphaZero
pub struct GumbelSampler {
    rng: Rng,
}

impl GumbelSampler {
    pub fn new() -> Self {
        Self {
            rng: Rng::default(),
        }
    }

    /// Generate Gumbel noise from Gumbel(0, 1) distribution
    /// Using the inverse transform method: -ln(-ln(U)) where U ~ Uniform(0,1)
    pub fn gumbel_noise(&mut self) -> f32 {
        let u = self.rng.next_f32_range(f32::EPSILON, 1.0);
        -(-u.ln()).ln()
    }

    /// Generate Gumbel noise for multiple actions
    pub fn gumbel_noise_vec(&mut self, n: usize) -> Vec<f32> {
        (0..n).map(|_| self.gumbel_noise()).collect()
    }

    /// Compute logits + Gumbel noise for action selection
    pub fn compute_gumbel_values(&mut self, logits: &[f32], temperature: f32) -> Vec<f32> {
        let noise = self.gumbel_noise_vec(logits.len());
        logits
            .iter()
            .zip(noise.iter())
            .map(|(logit, noise)| logit + temperature * noise)
            .collect()
    }

    /// Sample k actions without replacement using Gumbel-Top-k trick
    pub fn gumbel_top_k(&mut self, logits: &[f32], k: usize, temperature: f32) -> Vec<usize> {
        let gumbel_values = self.compute_gumbel_values(logits, temperature);
        
        let mut indices: Vec<usize> = (0..logits.len()).collect();
        indices.sort_by(|&a, &b| {
            gumbel_values[b]
                .partial_cmp(&gumbel_values[a])
                .unwrap_or(std::cmp::Ordering::Equal)
        });
        
        indices.truncate(k);
        indices
    }
}

/// Sequential halving for root action selection
pub struct SequentialHalving {
    /// Total simulation budget
    budget: usize,
    /// Current phase
    phase: usize,
    /// Remaining actions
    active_actions: Vec<usize>,
    /// Visit counts per action
    visits: Vec<usize>,
    /// Accumulated rewards per action
    rewards: Vec<f32>,
}

impl SequentialHalving {
    pub fn new(num_actions: usize, budget: usize) -> Self {
        let active_actions: Vec<usize> = (0..num_actions).collect();
        let visits = vec![0; num_actions];
        let rewards = vec![0.0; num_actions];
        
        Self {
            budget,
            phase: 0,
            active_actions,
            visits,
            rewards,
        }
    }

    /// Calculate number of phases (how many times we can halve)
    fn num_phases(&self) -> usize {
        let mut n = self.active_actions.len();
        let mut phases = 0;
        while n > 1 {
            n = (n + 1) / 2; // Ceiling division
            phases += 1;
        }
        phases
    }

    /// Get next action to simulate according to sequential halving
    pub fn next_action(&self) -> Option<usize> {
        if self.active_actions.is_empty() {
            return None;
        }

        // Find action with minimum visits among active actions
        self.active_actions
            .iter()
            .min_by_key(|&&action| self.visits[action])
            .copied()
    }

    /// Update with simulation result
    pub fn update(&mut self, action: usize, reward: f32) {
        self.visits[action] += 1;
        self.rewards[action] += reward;
        
        // Check if we should move to next phase
        let simulations_per_phase = self.budget / self.num_phases();
        let simulations_this_phase = self.active_actions
            .iter()
            .map(|&a| self.visits[a])
            .sum::<usize>();
        
        if simulations_this_phase >= simulations_per_phase * self.active_actions.len() {
            self.advance_phase();
        }
    }

    /// Advance to next phase by eliminating worst half
    fn advance_phase(&mut self) {
        if self.active_actions.len() <= 1 {
            return;
        }

        // Calculate average rewards
        let mut action_scores: Vec<(usize, f32)> = self.active_actions
            .iter()
            .map(|&action| {
                let avg_reward = if self.visits[action] > 0 {
                    self.rewards[action] / self.visits[action] as f32
                } else {
                    f32::NEG_INFINITY
                };
                (action, avg_reward)
            })
            .collect();

        // Sort by score (descending)
        action_scores.sort_by(|a, b| b.1.partial_cmp(&a.1).unwrap_or(std::cmp::Ordering::Equal));

        // Keep top half
        let keep_count = (self.active_actions.len() + 1) / 2;
        self.active_actions = action_scores
            .into_iter()
            .take(keep_count)
            .map(|(action, _)| action)
            .collect();
        
        self.phase += 1;
    }

    /// Get best action based on current statistics
    pub fn best_action(&self) -> Option<usize> {
        self.active_actions
            .iter()
            .max_by_key(|&&action| {
                if self.visits[action] > 0 {
                    ((self.rewards[action] / self.visits[action] as f32) * SCALE) as i64
                } else {
                    i64::MIN
                }
            })
            .copied()
    }
}

/// Compute completed Q-values using Gumbel max
pub fn completed_q_value(
    q_value: f32,
    visit_count: u32,
    policy_prior: f32,
    num_actions: usize,
    c_visit: f32,
    temperature: f32,
) -> f32 {
    if visit_count == 0 {
        return q_value;
    }

    // Gumbel max correction term
    let gumbel_max = temperature * (num_actions as f32).ln();
    
    // Completed Q-value formula from the paper
    let exploration_bonus = c_visit * policy_prior / (visit_count as f32).sqrt();
    
    q_value + exploration_bonus + gumbel_max / (visit_count as f32 + 1.0)
}

/// Select actions at root using Gumbel sampling
pub fn select_root_action_gumbel(
    moves: &[MoveEdge],
    policy_priors: &[f32],
    sampler: &mut GumbelSampler,
    temperature: f32,
) -> usize {
    // Convert policy priors to logits (log probabilities)
    let logits: Vec<f32> = policy_priors
        .iter()
        .map(|&p| (p.max(f32::EPSILON)).ln())
        .collect();
    
    // Sample single action using Gumbel-Top-1
    let selected = sampler.gumbel_top_k(&logits, 1, temperature);
    selected[0]
}